// IndexTwo Modern JavaScript

// Configuration
const CONFIG = {
    FEATURED_CATEGORIES: [
        'Business',
        'Weather', 
        'Utilities',
        'Finance',
        'Health & Fitness',
        'Entertainment'
    ],
    APPS_PER_CATEGORY: 6,
    ANIMATION_DELAY: 100
};

// State management
let appData = [];
let isLoading = false;

// DOM elements
const elements = {
    searchForm: null,
    searchInput: null,
    mobileMenuToggle: null,
    backToTop: null,
    categoriesDropdown: null,
    hotCategories: null,
    totalApps: null,
    totalCategories: null
};

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    try {
        initializeElements();
        setupEventListeners();
        await loadAppData();
        await initializeCategories();
        await loadFeaturedApps();
        initializeAnimations();
        updateStats();
    } catch (error) {
        console.error('Failed to initialize application:', error);
        showErrorMessage('Failed to load application data. Please refresh the page.');
    }
});

// Initialize DOM elements
function initializeElements() {
    elements.searchForm = document.getElementById('searchForm');
    elements.searchInput = document.getElementById('searchInput');
    elements.mobileMenuToggle = document.getElementById('mobileMenuToggle');
    elements.backToTop = document.getElementById('backToTop');
    elements.categoriesDropdown = document.getElementById('categoriesDropdown');
    elements.hotCategories = document.getElementById('hotCategories');
    elements.totalApps = document.getElementById('totalApps');
    elements.totalCategories = document.getElementById('totalCategories');
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    if (elements.searchForm) {
        elements.searchForm.addEventListener('submit', handleSearch);
    }

    // Mobile menu toggle
    if (elements.mobileMenuToggle) {
        elements.mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }

    // Back to top button
    if (elements.backToTop) {
        elements.backToTop.addEventListener('click', scrollToTop);
        window.addEventListener('scroll', handleScroll);
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Load app data
async function loadAppData() {
    if (typeof window.appData !== 'undefined' && window.appData.length > 0) {
        appData = window.appData;
        return;
    }

    try {
        isLoading = true;
        const response = await fetch('./data/typeList.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        appData = await response.json();
        window.appData = appData; // Cache globally
    } catch (error) {
        console.error('Error loading app data:', error);
        throw error;
    } finally {
        isLoading = false;
    }
}

// Initialize categories dropdown and footer
async function initializeCategories() {
    const categories = getUniqueCategories();
    
    // Populate categories dropdown
    if (elements.categoriesDropdown) {
        populateCategoriesDropdown(categories);
    }

    // Populate hot categories in footer
    if (elements.hotCategories) {
        populateHotCategories(categories.slice(0, 6));
    }
}

// Get unique categories from app data
function getUniqueCategories() {
    const categorySet = new Set();
    appData.forEach(app => {
        if (app.category && app.category.trim()) {
            categorySet.add(app.category.trim());
        }
    });
    return Array.from(categorySet).sort();
}

// Populate categories dropdown
function populateCategoriesDropdown(categories) {
    const dropdown = elements.categoriesDropdown;
    dropdown.innerHTML = '';

    categories.slice(0, 8).forEach(category => {
        const li = document.createElement('li');
        const a = document.createElement('a');
        a.href = `./type.html?category=${encodeURIComponent(category)}`;
        a.textContent = category;
        li.appendChild(a);
        dropdown.appendChild(li);
    });
}

// Populate hot categories in footer
function populateHotCategories(categories) {
    const hotCategoriesContainer = elements.hotCategories;
    hotCategoriesContainer.innerHTML = '';

    categories.forEach(category => {
        const li = document.createElement('li');
        const a = document.createElement('a');
        a.href = `./type.html?category=${encodeURIComponent(category)}`;
        a.textContent = category;
        li.appendChild(a);
        hotCategoriesContainer.appendChild(li);
    });
}

// Load featured apps for each category
async function loadFeaturedApps() {
    const loadPromises = CONFIG.FEATURED_CATEGORIES.map((category, index) => {
        return new Promise(resolve => {
            setTimeout(() => {
                loadCategoryApps(category);
                resolve();
            }, index * CONFIG.ANIMATION_DELAY);
        });
    });

    await Promise.all(loadPromises);
}

// Load apps for a specific category
function loadCategoryApps(category) {
    const categoryApps = appData.filter(app => 
        app.category && app.category.trim() === category
    );

    const shuffledApps = shuffleArray([...categoryApps]);
    const selectedApps = shuffledApps.slice(0, CONFIG.APPS_PER_CATEGORY);

    const containerId = getCategoryContainerId(category);
    const container = document.getElementById(containerId);

    if (container) {
        renderCategoryApps(container, selectedApps);
    }
}

// Get container ID for category
function getCategoryContainerId(category) {
    const categoryMap = {
        'Business': 'businessApps',
        'Weather': 'weatherApps',
        'Utilities': 'utilitiesApps',
        'Finance': 'financeApps',
        'Health & Fitness': 'healthApps',
        'Entertainment': 'entertainmentApps'
    };
    return categoryMap[category] || '';
}

// Render apps in category container
function renderCategoryApps(container, apps) {
    container.innerHTML = '';

    if (apps.length === 0) {
        container.innerHTML = '<p class="no-apps-message">No apps available</p>';
        return;
    }

    apps.forEach((app, index) => {
        const appElement = createAppElement(app, index);
        container.appendChild(appElement);
    });
}

// Create app element
function createAppElement(app, index) {
    const appDiv = document.createElement('div');
    appDiv.className = 'app-item-modern';
    appDiv.style.animationDelay = `${index * 50}ms`;

    const link = document.createElement('a');
    link.href = `./detail.html?id=${app.id}`;
    link.className = 'app-link-modern';

    const img = document.createElement('img');
    img.src = app.icon || './images/default-app-icon.png';
    img.alt = app.name || 'App Icon';
    img.className = 'app-icon-modern';
    img.loading = 'lazy';

    // Handle image load errors
    img.onerror = function() {
        this.src = './images/default-app-icon.png';
    };

    const name = document.createElement('div');
    name.className = 'app-name-modern';
    name.textContent = app.name || 'Unknown App';
    name.title = app.name || 'Unknown App';

    link.appendChild(img);
    link.appendChild(name);
    appDiv.appendChild(link);

    return appDiv;
}

// Utility function to shuffle array
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// Handle search form submission
function handleSearch(e) {
    e.preventDefault();
    const query = elements.searchInput.value.trim();
    if (query) {
        window.location.href = `./search.html?q=${encodeURIComponent(query)}`;
    }
}

// Toggle mobile menu
function toggleMobileMenu() {
    elements.mobileMenuToggle.classList.toggle('active');
    // Add mobile menu implementation here if needed
}

// Handle scroll events
function handleScroll() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    // Show/hide back to top button
    if (elements.backToTop) {
        if (scrollTop > 300) {
            elements.backToTop.classList.add('visible');
        } else {
            elements.backToTop.classList.remove('visible');
        }
    }
}

// Scroll to top
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Initialize animations
function initializeAnimations() {
    // Add fade-in animation to category cards
    const categoryCards = document.querySelectorAll('.category-card-modern');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    categoryCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
}

// Update statistics
function updateStats() {
    if (elements.totalApps && appData.length > 0) {
        const totalApps = appData.length;
        elements.totalApps.textContent = formatNumber(totalApps);
    }

    if (elements.totalCategories) {
        const categories = getUniqueCategories();
        elements.totalCategories.textContent = categories.length.toString();
    }
}

// Format number for display
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M+';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K+';
    }
    return num.toString();
}

// Show error message
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message-modern';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f56565;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
        errorDiv.remove();
    }, 5000);
}

// Export functions for global access
window.IndexTwoApp = {
    loadAppData,
    getUniqueCategories,
    formatNumber
};
