/* 
.app-banner {
	width: 100%;
	max-width: 1280px;
	margin: auto;
	background: linear-gradient(to right, #2c3e50 0%, #4a235a 100%);
	border-radius: 20px;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	overflow: hidden;
	position: relative;
	color: white;
	display: flex;
	flex-direction: column;
	border: 2px solid #d4af37;
}

.banner-header {
	padding: 30px 40px 20px;
	display: flex;
	align-items: center;
	background: rgba(0, 0, 0, 0.15);
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.app-icon-large {
	width: 100px;
	height: 100px;
	border-radius: 22px;
	background: white;
	box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
	margin-right: 25px;
	overflow: hidden;
	display: flex;
	justify-content: center;
	align-items: center;
	border: 2px solid #d4af37;
}

.app-icon-large img {
	width: 85%;
	height: 85%;
	object-fit: contain;
}

.app-info {
	flex: 1;
}

.app-name {
	font-family: 'Merriweather', serif;
	font-size: 2.5rem;
	font-weight: 700;
	margin-bottom: 5px;
	color: #d4af37;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.app-publisher {
	font-size: 1.2rem;
	opacity: 0.9;
	margin-bottom: 15px;
	font-weight: 500;
	color: #ecf0f1;
}

.app-rating {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.rating-stars {
	display: flex;
	margin-right: 15px;
}

.rating-stars svg {
	width: 26px;
	height: 26px;
	fill: #FFD700;
	margin-right: 5px;
}

.rating-text {
	font-size: 1.3rem;
	font-weight: 600;
	background: rgba(0, 0, 0, 0.2);
	padding: 5px 15px;
	border-radius: 50px;
}

.banner-content {
	display: flex;
	padding: 30px 40px;
}

.app-description {
	flex: 1;
	padding-right: 40px;
}

.description-title {
	font-family: 'Merriweather', serif;
	font-size: 1.8rem;
	font-weight: 700;
	margin-bottom: 20px;
	color: #d4af37;
	border-left: 4px solid #d4af37;
	padding-left: 15px;
}

.description-text {
	font-size: 1.15rem;
	line-height: 1.7;
	margin-bottom: 30px;
	opacity: 0.95;
	max-width: 700px;
	color: #ecf0f1;
}

.app-highlights {
	margin-bottom: 25px;
}

.highlight-item {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
	font-size: 1.1rem;
}

.highlight-item i {
	color: #d4af37;
	margin-right: 12px;
	font-size: 1.2rem;
}

.download-btn {
	display: inline-flex;
	align-items: center;
	background: linear-gradient(to right, #d4af37 0%, #b8860b 100%);
	color: #2c3e50;
	font-size: 1.3rem;
	font-weight: 700;
	padding: 16px 35px;
	border-radius: 50px;
	text-decoration: none;
	box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
	transition: all 0.3s ease;
	border: 2px solid #f1c40f;
	font-family: 'Merriweather', serif;
}

.download-btn:hover {
	transform: translateY(-3px);
	box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
	background: linear-gradient(to right, #f1c40f 0%, #d4af37 100%);
}

.download-btn i {
	margin-right: 12px;
	font-size: 1.5rem;
}

.screenshots {
	flex: 1;
	display: flex;
	justify-content: flex-end;
	position: relative;
}

.screenshot-container {
	display: flex;
	gap: 20px;
}

.screenshot {
	width: 180px;
	height: 320px;
	border-radius: 20px;
	overflow: hidden;
	box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	border: 8px solid #1a1a1a;
}

.screenshot img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.screenshot::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 30px;
	background: #1a1a1a;
	border-radius: 12px 12px 0 0;
}

.screenshot::after {
	content: "";
	position: absolute;
	bottom: 10px;
	width: 40%;
	height: 5px;
	background: #1a1a1a;
	border-radius: 5px;
}

.screenshot:nth-child(1) {
	transform: rotate(-5deg);
	z-index: 3;
	margin-top: -20px;
}

.screenshot:nth-child(2) {
	transform: rotate(3deg);
	z-index: 2;
}

.screenshot:nth-child(3) {
	transform: rotate(7deg);
	z-index: 1;
	margin-top: 20px;
}

.banner-footer {
	padding: 25px 40px;
	background: rgba(0, 0, 0, 0.15);
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 1.1rem;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.app-category {
	display: flex;
	align-items: center;
}

.app-category i {
	margin-right: 10px;
	color: #d4af37;
}

.app-size {
	display: flex;
	align-items: center;
}

.app-size i {
	margin-right: 10px;
	color: #d4af37;
}

.app-version {
	display: flex;
	align-items: center;
}

.app-version i {
	margin-right: 10px;
	color: #d4af37;
}

.decoration {
	position: absolute;
	opacity: 0.1;
	pointer-events: none;
}

.circle-1 {
	width: 300px;
	height: 300px;
	border-radius: 50%;
	background: #d4af37;
	top: -150px;
	right: -150px;
}

.circle-2 {
	width: 200px;
	height: 200px;
	border-radius: 50%;
	background: #8e44ad;
	bottom: -100px;
	left: -100px;
}

.cross {
	position: absolute;
	font-size: 120px;
	color: rgba(212, 175, 55, 0.08);
	top: 40%;
	left: 20%;
	transform: rotate(15deg);
}

@media (max-width: 900px) {
	.banner-content {
		flex-direction: column;
	}
	
	.app-description {
		padding-right: 0;
		margin-bottom: 40px;
	}
	
	.screenshots {
		justify-content: center;
	}
	
	.screenshot-container {
		width: 100%;
		justify-content: center;
	}
	
	.screenshot {
		width: 150px;
		height: 280px;
	}
	
	.banner-footer {
		flex-direction: column;
		gap: 15px;
		align-items: flex-start;
	}
}

@media (max-width: 600px) {
	.banner-header {
		flex-direction: column;
		text-align: center;
		padding-bottom: 30px;
	}
	
	.app-icon-large {
		margin-right: 0;
		margin-bottom: 20px;
	}
	
	.app-rating {
		justify-content: center;
	}
	
	.screenshot-container {
		flex-direction: column;
		align-items: center;
	}
	
	.screenshot {
		width: 180px;
		height: 320px;
	}
	
	.screenshot:nth-child(1),
	.screenshot:nth-child(2),
	.screenshot:nth-child(3) {
		transform: rotate(0);
		margin: 10px 0;
	}
	
	.app-name {
		font-size: 2.0rem;
	}
} */