/* App Details page specific styles */

/* App Details Section */
.app-details {
    padding: var(--spacing-2xl) 0;
}

#screenshotsList{
    overflow-y: hidden;
}

/* App Header */
.app-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-2xl);
    border-bottom: 1px solid var(--border-color);
}

.app-icon-container {
    flex-shrink: 0;
}

.app-icon {
    width: 120px;
    height: 120px;
    border-radius: var(--radius-2xl);
    object-fit: cover;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.app-info {
    flex: 1;
    min-width: 0;
}

.app-name {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    line-height: 1.2;
}

.app-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.app-publisher {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
}

.app-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.app-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.rating-stars {
    display: flex;
    gap: 3px;
}

.star {
    width: 18px;
    height: 18px;
    fill: var(--warning-color);
}

.star.empty {
    fill: var(--text-tertiary);
}

.rating-text {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    font-weight: 500;
}

.app-category {
    background-color: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

/* App Actions */
.app-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.download-btn,
.share-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
}

.download-btn {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
}

.download-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.share-btn {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.share-btn:hover {
    background-color: var(--background-tertiary);
    border-color: var(--primary-color);
}

/* Screenshots Section */
.screenshots-section {
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.screenshots-container {
    position: relative;
    /* overflow-y: hidden; */
}

.screenshots-list {
    display: flex;
    gap: var(--spacing-md);
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
    scroll-behavior: smooth;
}

.screenshots-list::-webkit-scrollbar {
    height: 6px;
}

.screenshots-list::-webkit-scrollbar-track {
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
}

.screenshots-list::-webkit-scrollbar-thumb {
    background: var(--text-tertiary);
    border-radius: var(--radius-sm);
}

.screenshots-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

.screenshot-item {
    flex-shrink: 0;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.screenshot-item:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.screenshot-item img {
    width: 200px;
    height: 356px;
    object-fit: cover;
    display: block;
}

/* Description Section */
.description-section {
    margin-bottom: var(--spacing-2xl);
}

.app-description {
    font-size: var(--font-size-base);
    line-height: 1.7;
    color: var(--text-primary);
}

.app-description p {
    margin-bottom: var(--spacing-md);
}

.app-description p:last-child {
    margin-bottom: 0;
}

/* Information Section */
.info-section {
    margin-bottom: var(--spacing-2xl);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    background-color: var(--background-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.info-value {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    font-weight: 600;
}

/* Related Apps Section */
.related-section {
    margin-bottom: var(--spacing-2xl);
}

.related-apps {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.related-app-card {
    background-color: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    cursor: pointer;
}

.related-app-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.related-app-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.related-app-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    flex-shrink: 0;
}

.related-app-info {
    flex: 1;
    min-width: 0;
}

.related-app-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-app-publisher {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.related-app-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.related-app-rating .star {
    width: 12px;
    height: 12px;
}

/* Screenshot Modal */
.screenshot-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.screenshot-modal.visible {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    z-index: 10001;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

#modalImage {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

.modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
}

.nav-btn {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    pointer-events: all;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

.prev-btn {
    margin-left: -60px;
}

.next-btn {
    margin-right: -60px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .app-icon {
        width: 100px;
        height: 100px;
    }

    .app-name {
        font-size: var(--font-size-3xl);
    }

    .app-subtitle {
        font-size: var(--font-size-lg);
    }

    .app-meta {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .app-actions {
        justify-content: center;
        width: 100%;
    }

    .download-btn,
    .share-btn {
        flex: 1;
        justify-content: center;
        min-width: 140px;
    }

    .screenshot-item img {
        width: 160px;
        height: 284px;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    .related-apps {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .modal-nav {
        display: none;
    }

    .modal-close {
        top: -30px;
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .app-details {
        padding: var(--spacing-xl) 0;
    }

    .app-name {
        font-size: var(--font-size-2xl);
    }

    .app-subtitle {
        font-size: var(--font-size-base);
    }

    .app-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .download-btn,
    .share-btn {
        width: 100%;
    }

    .screenshot-item img {
        width: 140px;
        height: 248px;
    }

    .section-title {
        font-size: var(--font-size-xl);
    }

    .info-grid {
        padding: var(--spacing-md);
    }
}
