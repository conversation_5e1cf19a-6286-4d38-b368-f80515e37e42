/* Common styles for all pages */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Apple-inspired color palette */
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;

    /* Neutral colors */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #C7C7CC;
    --background-primary: #FFFFFF;
    --background-secondary: #F2F2F7;
    --background-tertiary: #E5E5EA;
    --border-color: #D1D1D6;

    /* Dark mode colors */
    --dark-text-primary: #FFFFFF;
    --dark-text-secondary: #98989D;
    --dark-background-primary: #000000;
    --dark-background-secondary: #1C1C1E;
    --dark-background-tertiary: #2C2C2E;
    --dark-border-color: #38383A;

    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ubu<PERSON><PERSON>, Cantarell, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Lazy loading styles */
.lazy-load {
    opacity: 0.7;
    transition: opacity var(--transition-normal);
    background-color: var(--background-secondary);
    /* Background image will be set dynamically by JavaScript */
    background-repeat: no-repeat;
    background-position: center;
    background-size: 32px 32px;
}

.lazy-loaded {
    opacity: 1;
    background-image: none;
}

.lazy-error {
    opacity: 0.5;
    background-color: var(--background-tertiary);
    background-image: none;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-xl);
    }
}

/* Header */
.header {
    background-color:#246330;
    /* border-bottom: 1px solid var(--border-color); */
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    gap: var(--spacing-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-lg);
}

.logo img {
    width: auto;
    height: 50px;
    border-radius: var(--radius-sm);
}

/* Navigation */
.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: var(--background-secondary);
}

/* Dropdown */
.nav-dropdown {
    position: relative;
}

.dropdown-menu {
    /* background-color: var(--background-primary); */
    opacity: revert;
    visibility: visible;
    z-index: 1000;
    list-style: none;
    display: flex;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: #FFFFFF;
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.dropdown-menu a:hover {
    background-color: var(--background-secondary);
    color: var(--primary-color);
}

/* Search */
.search-container {
    flex: 1;
    max-width: 260px;
}

.search-form {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-right: 3rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    background-color: var(--background-secondary);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--background-primary);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-btn {
    position: absolute;
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.search-btn:hover {
    color: var(--primary-color);
    background-color: var(--background-tertiary);
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    gap: 3px;
}

.mobile-menu-toggle span {
    width: 20px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all var(--transition-fast);
}

/* Footer */
.footer {
    background-color:#246330;
    border-top: 1px solid var(--border-color);
    margin-top: var(--spacing-2xl);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    padding-bottom: 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    font-size: var(--font-size-lg);
}

.footer-logo img {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
}

.footer-description {
    color: #FFFFFF;
    line-height: 1.6;
}

.footer-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: #FFFFFF;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: #FFFFFF;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-logo span{
    color: #FFFFFF;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    color: #FFFFFF;
    font-size: 18px;
    background-color: #143a1c;
    padding: 50px 0;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 48px;
    height: 48px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all var(--transition-normal);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Loading spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.loading-spinner.visible {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--background-tertiary);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .header-content {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }

    .nav {
        display: none;
        width: 100%;
        order: 3;
    }

    .nav.mobile-open {
        display: block;
    }

    .nav-list {
        flex-direction: column;
        align-items: stretch;
        gap: 0;
        background-color: var(--background-secondary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        margin-top: var(--spacing-md);
    }

    .nav-link {
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .search-container {
        order: 2;
        flex: 1;
        min-width: 200px;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: none;
        padding: 0;
        margin-left: var(--spacing-md);
    }

    .dropdown-menu a {
        padding-left: var(--spacing-xl);
        font-size: var(--font-size-sm);
    }

    .back-to-top {
        bottom: var(--spacing-md);
        right: var(--spacing-md);
        width: 44px;
        height: 44px;
    }
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}