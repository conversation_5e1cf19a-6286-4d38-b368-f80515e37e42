<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppStore Discovery Hub</title>
    <link rel="stylesheet" href="css/indexTwo.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/base.css">
</head>
<body>
    <!-- Header -->
    <header class="header-modern">
        <div class="container">
            <div class="header-content-modern">
                <div class="logo-modern">
                    <a href="indexTwo.html">
                        <img src="images/logo.png" alt="AppStore Discovery Hub Logo">
                        <span class="logo-text">Discovery Hub</span>
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="nav-modern">
                    <ul class="dropdown-menu-modern" id="categoriesDropdown">
                        <!-- Categories will be loaded dynamically -->
                    </ul>
                </nav>

                <!-- Search Bar -->
                <div class="search-container-modern">
                    <form class="search-form-modern" id="searchForm">
                        <input type="text" class="search-input-modern" id="searchInput" placeholder="Discover amazing apps...">
                        <button type="submit" class="search-btn-modern">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </form>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle-modern" id="mobileMenuToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-modern">
        <!-- Hero Section -->
        <section class="hero-modern">
            <div class="container">
                <div class="hero-grid">
                    <div class="hero-content-modern">
                        <h1 class="hero-title-modern">
                            <span class="title-accent">Discover</span>
                            <span class="title-main">Premium Apps</span>
                            <span class="title-sub">for Every Need</span>
                        </h1>
                        <p class="hero-description">
                            Explore our curated collection of top-rated applications across essential categories. 
                            Find the perfect tools to enhance your digital lifestyle.
                        </p>
                        <div class="hero-actions-modern">
                            <a href="#categories" class="btn-primary-modern">
                                <span>Explore Categories</span>
                                <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 8h6m0 0L8 14m4-6L8 2"/>
                                </svg>
                            </a>
                            <a href="search.html" class="btn-secondary-modern">
                                <span>Advanced Search</span>
                            </a>
                        </div>
                    </div>
                    <div class="hero-visual">
                        <div class="hero-stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" id="totalApps">11K+</div>
                                <div class="stat-label">Premium Apps</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="totalCategories">24</div>
                                <div class="stat-label">Categories</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">4.8★</div>
                                <div class="stat-label">Avg Rating</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">100M+</div>
                                <div class="stat-label">Downloads</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section class="categories-modern" id="categories">
            <div class="container">
                <div class="section-header-modern">
                    <h2 class="section-title-modern">Featured Categories</h2>
                    <p class="section-subtitle-modern">Discover apps across our most popular categories</p>
                </div>

                <div class="categories-grid-modern">
                    <!-- Business Category -->
                    <div class="category-card-modern business-card">
                        <div class="category-header-modern">
                            <div class="category-icon-modern">💼</div>
                            <div class="category-info">
                                <h3 class="category-title-modern">Business</h3>
                                <p class="category-desc">Professional productivity tools</p>
                            </div>
                            <a href="./type.html?category=Business" class="category-link-modern">
                                <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 10h6m0 0l-3-3m3 3l-3 3"/>
                                </svg>
                            </a>
                        </div>
                        <div class="category-apps-modern" id="businessApps">
                            <!-- Business apps will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Weather Category -->
                    <div class="category-card-modern weather-card">
                        <div class="category-header-modern">
                            <div class="category-icon-modern">🌤️</div>
                            <div class="category-info">
                                <h3 class="category-title-modern">Weather</h3>
                                <p class="category-desc">Stay informed with forecasts</p>
                            </div>
                            <a href="./type.html?category=Weather" class="category-link-modern">
                                <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 10h6m0 0l-3-3m3 3l-3 3"/>
                                </svg>
                            </a>
                        </div>
                        <div class="category-apps-modern" id="weatherApps">
                            <!-- Weather apps will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Utilities Category -->
                    <div class="category-card-modern utilities-card">
                        <div class="category-header-modern">
                            <div class="category-icon-modern">🔧</div>
                            <div class="category-info">
                                <h3 class="category-title-modern">Utilities</h3>
                                <p class="category-desc">Essential device tools</p>
                            </div>
                            <a href="./type.html?category=Utilities" class="category-link-modern">
                                <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 10h6m0 0l-3-3m3 3l-3 3"/>
                                </svg>
                            </a>
                        </div>
                        <div class="category-apps-modern" id="utilitiesApps">
                            <!-- Utilities apps will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Finance Category -->
                    <div class="category-card-modern finance-card">
                        <div class="category-header-modern">
                            <div class="category-icon-modern">💰</div>
                            <div class="category-info">
                                <h3 class="category-title-modern">Finance</h3>
                                <p class="category-desc">Money management solutions</p>
                            </div>
                            <a href="./type.html?category=Finance" class="category-link-modern">
                                <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 10h6m0 0l-3-3m3 3l-3 3"/>
                                </svg>
                            </a>
                        </div>
                        <div class="category-apps-modern" id="financeApps">
                            <!-- Finance apps will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Health & Fitness Category -->
                    <div class="category-card-modern health-card">
                        <div class="category-header-modern">
                            <div class="category-icon-modern">💪</div>
                            <div class="category-info">
                                <h3 class="category-title-modern">Health & Fitness</h3>
                                <p class="category-desc">Wellness and fitness apps</p>
                            </div>
                            <a href="./type.html?category=Health%20%26%20Fitness" class="category-link-modern">
                                <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 10h6m0 0l-3-3m3 3l-3 3"/>
                                </svg>
                            </a>
                        </div>
                        <div class="category-apps-modern" id="healthApps">
                            <!-- Health apps will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Entertainment Category -->
                    <div class="category-card-modern entertainment-card">
                        <div class="category-header-modern">
                            <div class="category-icon-modern">🎬</div>
                            <div class="category-info">
                                <h3 class="category-title-modern">Entertainment</h3>
                                <p class="category-desc">Fun and media apps</p>
                            </div>
                            <a href="./type.html?category=Entertainment" class="category-link-modern">
                                <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 10h6m0 0l-3-3m3 3l-3 3"/>
                                </svg>
                            </a>
                        </div>
                        <div class="category-apps-modern" id="entertainmentApps">
                            <!-- Entertainment apps will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Additional Categories Section -->
        <section class="additional-categories-modern">
            <div class="container">
                <div class="section-header-modern">
                    <h2 class="section-title-modern">More Categories</h2>
                    <p class="section-subtitle-modern">Explore additional app categories for every need</p>
                </div>

                <div class="additional-grid-modern">
                    <!-- Productivity Category -->
                    <div class="additional-category-card">
                        <div class="additional-category-header">
                            <div class="additional-category-icon">📊</div>
                            <div class="additional-category-info">
                                <h3 class="additional-category-title">Productivity</h3>
                                <p class="additional-category-count" id="productivityCount">0 apps</p>
                            </div>
                        </div>
                        <div class="additional-category-apps" id="productivityApps"></div>
                        <a href="./type.html?category=Productivity" class="additional-category-link">View All →</a>
                    </div>

                    <!-- Education Category -->
                    <div class="additional-category-card">
                        <div class="additional-category-header">
                            <div class="additional-category-icon">🎓</div>
                            <div class="additional-category-info">
                                <h3 class="additional-category-title">Education</h3>
                                <p class="additional-category-count" id="educationCount">0 apps</p>
                            </div>
                        </div>
                        <div class="additional-category-apps" id="educationApps"></div>
                        <a href="./type.html?category=Education" class="additional-category-link">View All →</a>
                    </div>

                    <!-- Photo & Video Category -->
                    <div class="additional-category-card">
                        <div class="additional-category-header">
                            <div class="additional-category-icon">📸</div>
                            <div class="additional-category-info">
                                <h3 class="additional-category-title">Photo & Video</h3>
                                <p class="additional-category-count" id="photoVideoCount">0 apps</p>
                            </div>
                        </div>
                        <div class="additional-category-apps" id="photoVideoApps"></div>
                        <a href="./type.html?category=Photo%20%26%20Video" class="additional-category-link">View All →</a>
                    </div>

                    <!-- Music Category -->
                    <div class="additional-category-card">
                        <div class="additional-category-header">
                            <div class="additional-category-icon">🎵</div>
                            <div class="additional-category-info">
                                <h3 class="additional-category-title">Music</h3>
                                <p class="additional-category-count" id="musicCount">0 apps</p>
                            </div>
                        </div>
                        <div class="additional-category-apps" id="musicApps"></div>
                        <a href="./type.html?category=Music" class="additional-category-link">View All →</a>
                    </div>

                    <!-- Travel Category -->
                    <div class="additional-category-card">
                        <div class="additional-category-header">
                            <div class="additional-category-icon">✈️</div>
                            <div class="additional-category-info">
                                <h3 class="additional-category-title">Travel</h3>
                                <p class="additional-category-count" id="travelCount">0 apps</p>
                            </div>
                        </div>
                        <div class="additional-category-apps" id="travelApps"></div>
                        <a href="./type.html?category=Travel" class="additional-category-link">View All →</a>
                    </div>

                    <!-- Social Networking Category -->
                    <div class="additional-category-card">
                        <div class="additional-category-header">
                            <div class="additional-category-icon">👥</div>
                            <div class="additional-category-info">
                                <h3 class="additional-category-title">Social Networking</h3>
                                <p class="additional-category-count" id="socialCount">0 apps</p>
                            </div>
                        </div>
                        <div class="additional-category-apps" id="socialApps"></div>
                        <a href="./type.html?category=Social%20Networking" class="additional-category-link">View All →</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Apps Section -->
        <section class="popular-apps-modern">
            <div class="container">
                <div class="section-header-modern">
                    <h2 class="section-title-modern">Trending Apps</h2>
                    <p class="section-subtitle-modern">Most popular apps across all categories</p>
                </div>

                <div class="popular-apps-grid" id="popularAppsGrid">
                    <!-- Popular apps will be loaded dynamically -->
                </div>

                <div class="popular-apps-actions">
                    <a href="./search.html" class="btn-outline-modern">Discover More Apps</a>
                </div>
            </div>
        </section>

        <!-- Quick Categories Section -->
        <section class="quick-categories-modern">
            <div class="container">
                <div class="section-header-modern">
                    <h2 class="section-title-modern">Browse by Category</h2>
                    <p class="section-subtitle-modern">Quick access to all available categories</p>
                </div>

                <div class="quick-categories-grid" id="quickCategoriesGrid">
                    <!-- Quick categories will be loaded dynamically -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer-modern">
        <div class="container">
            <div class="footer-content-modern">
                <div class="footer-section-modern">
                    <div class="footer-logo-modern">
                        <img src="images/logo.png" alt="AppStore Discovery Hub Logo">
                        <span>Discovery Hub</span>
                    </div>
                    <p class="footer-description-modern">
                        Your premium destination for discovering exceptional apps across all categories.
                        Curated selections for the modern digital lifestyle.
                    </p>
                </div>

                <div class="footer-section-modern">
                    <h3 class="footer-title-modern">Quick Access</h3>
                    <ul class="footer-links-modern">
                        <li><a href="./Privacy.html" id="privacyLink">Privacy Policy</a></li>
                        <li><a href="./Terms.html" id="termsLink">Terms of Service</a></li>
                    </ul>
                </div>

                <div class="footer-section-modern">
                    <h3 class="footer-title-modern">Popular Categories</h3>
                    <ul class="footer-links-modern" id="hotCategories">
                        <!-- Hot categories will be loaded dynamically -->
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bottom-modern">
            <p>&copy; 2025 AppStore Discovery Hub. All rights reserved.</p>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top-modern" id="backToTop">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
            <polyline points="18,15 12,9 6,15"></polyline>
        </svg>
    </button>

    <!-- Scripts -->
    <script src="js/common.js"></script>
    <script src="js/indexTwo.js"></script>
</body>
</html>
