/* Category page specific styles */

/* Category Header */
.category-header {
    background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
    padding: var(--spacing-2xl) 0;
    border-bottom: 1px solid var(--border-color);
}

.category-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xl);
}

.category-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 1;
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    box-shadow: var(--shadow-lg);
    flex-shrink: 0;
}

.category-details {
    flex: 1;
    min-width: 0;
}

.category-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    line-height: 1.2;
}

.category-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.category-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.app-count {
    font-weight: 600;
    color: var(--primary-color);
}

.separator {
    opacity: 0.5;
}

/* Category Controls */
.category-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.sort-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sort-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
}

.sort-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--background-primary);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 150px;
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.sort-select:hover {
    border-color: var(--primary-color);
}

/* Apps Section */
.apps-section {
    padding: var(--spacing-2xl) 0;
}

.apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* App Card Styles (Enhanced from common) */
.app-card {
    background-color: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.app-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.app-card:hover::before {
    left: 100%;
}

.app-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.app-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.app-icon {
    width: 72px;
    height: 72px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.app-info {
    flex: 1;
    min-width: 0;
}

.app-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.app-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.app-publisher {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.app-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.app-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.star {
    width: 14px;
    height: 14px;
    fill: var(--warning-color);
}

.star.empty {
    fill: var(--text-tertiary);
}

.rating-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-left: var(--spacing-xs);
    font-weight: 500;
}

.app-category {
    background-color: var(--background-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.app-updated {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.load-more-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    min-width: 160px;
}

.load-more-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.load-more-btn:disabled {
    background-color: var(--text-tertiary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* No Results */
.no-results {
    text-align: center;
    padding: var(--spacing-2xl) 0;
}

.no-results-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-results-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.no-results-text {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.back-home-btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: all var(--transition-fast);
}

.back-home-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Loading States */
.app-card.loading {
    pointer-events: none;
    opacity: 0.7;
}

.skeleton {
    background: linear-gradient(90deg, var(--background-secondary) 25%, var(--background-tertiary) 50%, var(--background-secondary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-icon {
    width: 72px;
    height: 72px;
    border-radius: var(--radius-lg);
}

.skeleton-line {
    height: 1rem;
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-xs);
}

.skeleton-title {
    width: 80%;
}

.skeleton-subtitle {
    width: 60%;
}

.skeleton-publisher {
    width: 50%;
}

.skeleton-rating {
    width: 40%;
}

.skeleton-category {
    width: 30%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .category-header {
        padding: var(--spacing-xl) 0;
    }
    
    .category-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-lg);
    }
    
    .category-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .category-icon {
        width: 64px;
        height: 64px;
        font-size: 2rem;
    }
    
    .category-title {
        font-size: var(--font-size-3xl);
    }
    
    .category-description {
        font-size: var(--font-size-base);
    }
    
    .category-controls {
        width: 100%;
        justify-content: center;
    }
    
    .apps-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .app-card {
        padding: var(--spacing-md);
    }
    
    .app-header {
        gap: var(--spacing-sm);
    }
    
    .app-icon {
        width: 60px;
        height: 60px;
    }
    
    .app-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .category-stats {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
    }
    
    .separator {
        display: none;
    }
    
    .sort-select {
        min-width: 120px;
        font-size: var(--font-size-xs);
    }
}
