/* IndexTwo Modern Design Styles */

/* CSS Custom Properties */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-light: #a0aec0;
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-card: #ffffff;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-secondary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Header Styles */
.header-modern {
    background: var(--bg-primary);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

.header-content-modern {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    gap: 2rem;
}

.logo-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-modern a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.25rem;
}

.logo-modern img {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
}

.logo-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation */
.nav-modern {
    flex: 1;
    max-width: 300px;
}

.dropdown-menu-modern {
    display: flex;
    list-style: none;
    gap: 1.5rem;
}

.dropdown-menu-modern li a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
}

.dropdown-menu-modern li a:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

/* Search Container */
.search-container-modern {
    flex: 1;
    max-width: 400px;
}

.search-form-modern {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-modern {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    font-size: 0.875rem;
    background: var(--bg-primary);
    transition: var(--transition-fast);
}

.search-input-modern:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn-modern {
    position: absolute;
    right: 0.5rem;
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-md);
    padding: 0.5rem;
    color: white;
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-btn-modern:hover {
    transform: scale(1.05);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle-modern {
    display: none;
    flex-direction: column;
    gap: 0.25rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle-modern span {
    width: 1.5rem;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.hero-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title-modern {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
}

.title-accent {
    color: #ffd700;
    font-size: 0.7em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.title-main {
    color: white;
}

.title-sub {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8em;
    font-weight: 600;
}

.hero-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-actions-modern {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-modern,
.btn-secondary-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border-radius: var(--border-radius-xl);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.btn-primary-modern {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-primary-modern:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary-modern {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* Hero Stats Grid */
.hero-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: #ffd700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Categories Section */
.categories-modern {
    padding: 5rem 0;
    background: var(--bg-primary);
}

.section-header-modern {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title-modern {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle-modern {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.categories-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.category-card-modern {
    background: var(--bg-card);
    border-radius: var(--border-radius-2xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.category-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.category-card-modern:hover::before {
    transform: scaleX(1);
}

.category-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.category-header-modern {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.category-icon-modern {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-xl);
}

.category-info {
    flex: 1;
}

.category-title-modern {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.category-desc {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.category-link-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-md);
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
}

.category-link-modern:hover {
    transform: scale(1.1);
}

.category-apps-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 1rem;
}

/* App Item Styles */
.app-item-modern {
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.app-link-modern {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-normal);
    padding: 0.75rem;
    border-radius: var(--border-radius-lg);
    background: var(--bg-secondary);
    border: 1px solid transparent;
}

.app-link-modern:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    border-color: var(--border-color);
    background: var(--bg-primary);
}

.app-icon-modern {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-md);
    margin-bottom: 0.5rem;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.app-link-modern:hover .app-icon-modern {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.app-name-modern {
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.3;
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    max-width: 100%;
}

.app-link-modern:hover .app-name-modern {
    color: var(--text-primary);
}

.no-apps-message {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--text-light);
    font-style: italic;
    padding: 2rem;
}

/* Category-specific gradients */
.business-card .category-icon-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.weather-card .category-icon-modern {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.utilities-card .category-icon-modern {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.finance-card .category-icon-modern {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.health-card .category-icon-modern {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2d3748;
}

.entertainment-card .category-icon-modern {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #2d3748;
}

/* Error message styles */
.error-message-modern {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #f56565;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Footer */
.footer-modern {
    background: var(--dark-gradient);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content-modern {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.footer-logo-modern img {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
}

.footer-description-modern {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-title-modern {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-links-modern {
    list-style: none;
}

.footer-links-modern li {
    margin-bottom: 0.5rem;
}

.footer-links-modern a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links-modern a:hover {
    color: white;
}

.footer-bottom-modern {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* Back to Top Button */
.back-to-top-modern {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top-modern.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top-modern:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content-modern {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .nav-modern,
    .search-container-modern {
        display: none;
    }
    
    .mobile-menu-toggle-modern {
        display: flex;
    }
    
    .hero-grid {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title-modern {
        font-size: 2.5rem;
    }
    
    .hero-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .categories-grid-modern {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer-content-modern {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title-modern {
        font-size: 2rem;
    }
    
    .hero-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .category-card-modern {
        padding: 1rem;
    }
    
    .hero-actions-modern {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary-modern,
    .btn-secondary-modern {
        width: 100%;
        justify-content: center;
    }
}
