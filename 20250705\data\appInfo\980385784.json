{"artistViewUrl": "https://apps.apple.com/us/developer/8-socks-llc/id*********?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/d7/c0/e7/d7c0e7bf-7f5e-0b48-d94d-2ec67930b873/AppIcon-0-0-1x_U007emarketing-0-0-0-7-0-0-sRGB-0-0-0-GLES2_U002c0-512MB-85-220-0-0.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/d7/c0/e7/d7c0e7bf-7f5e-0b48-d94d-2ec67930b873/AppIcon-0-0-1x_U007emarketing-0-0-0-7-0-0-sRGB-0-0-0-GLES2_U002c0-512MB-85-220-0-0.png/100x100bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/e0/9f/bc/e09fbc66-d419-bf2e-fcb5-cc192d132ae6/mzl.bnmvkxye.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/79/74/7d/79747d49-c1e8-c77d-5fe7-387946846a54/mzl.oqqndfpu.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/34/14/46/3414469d-287f-8b07-5700-291ed2f0fe6b/mzl.vmdskjpi.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/8e/51/c4/8e51c4f9-cdc9-da52-fbef-c97f03919e75/mzl.havtowzy.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/da/ef/1e/daef1e18-5789-9d8a-eba3-3c2fe004324f/mzl.xehlzwsm.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/9b/62/bb/9b62bbf7-17ea-b4f8-b797-955dcaa108de/pr_source.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/c9/50/8c/c9508c73-faae-9ee2-0dd3-949053db7f1f/pr_source.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/b1/f2/3d/b1f23dea-ae78-483c-4edd-dca04ee9b234/pr_source.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/06/9d/3f/069d3f3c-19f4-0c44-4ef6-a1744ce805a2/pr_source.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/d7/c0/e7/d7c0e7bf-7f5e-0b48-d94d-2ec67930b873/AppIcon-0-0-1x_U007emarketing-0-0-0-7-0-0-sRGB-0-0-0-GLES2_U002c0-512MB-85-220-0-0.png/512x512bb.jpg", "isGameCenterEnabled": false, "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "kind": "software", "trackCensoredName": "StreetFoodFinder", "averageUserRating": 4.80314, "trackViewUrl": "https://apps.apple.com/us/app/streetfoodfinder/id*********?uo=4", "contentAdvisoryRating": "4+", "formattedPrice": "Free", "averageUserRatingForCurrentVersion": 4.80314, "sellerUrl": "https://streetfoodfinder.com", "languageCodesISO2A": ["EN"], "fileSizeBytes": "24652800", "trackContentRating": "4+", "minimumOsVersion": "11.0", "userRatingCountForCurrentVersion": 1397, "artistId": *********, "artistName": "8 Socks, LLC", "genres": ["Food & Drink", "Travel"], "price": 0, "bundleId": "com.StreetFoodFinder.StreetFoodFinderApp", "releaseDate": "2015-04-07T15:58:39Z", "primaryGenreName": "Food & Drink", "primaryGenreId": 6023, "genreIds": ["6023", "6003"], "trackId": *********, "trackName": "StreetFoodFinder", "isVppDeviceBasedLicensingEnabled": true, "sellerName": "8 Socks, LLC", "currentVersionReleaseDate": "2022-06-30T18:44:58Z", "releaseNotes": "Bug Fixes & Updates :)", "version": "2.42", "wrapperType": "software", "currency": "USD", "description": "**CURRENTLY ONLY LAUNCHED IN SELECT CITIES (see below)**\n\nIn Ohio, Raleigh, LA, Phoenix, Nashville, Tampa? Find your next meal using StreetFoodFinder! SFF helps you find and choose which food truck / cart to eat at! Please do keep in mind that many trucks close for the winter in many locations.\n\nMore features coming soon! Visit our website to send us ideas and feedback!\n\nSFF has officially launched in the following cities. \nColumbus OH, Raleigh, NC, Durham, NC, Cincinnati OH, Corpus Christi, TX, Cleveland OH, Los Angeles CA, Phoenix AZ, Nashville TN, Tampa FL\n\nWant to launch SFF in your city?\nContact <NAME_EMAIL>", "userRatingCount": 1397}